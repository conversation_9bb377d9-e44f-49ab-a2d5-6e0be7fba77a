# MCP-Playwright Test Generation Instructions

## Prerequisites and Dependencies

Before running this project, ensure you have the following installed:

### Required Dependencies:
- **Node.js** (v18 or higher)
- **npm** (comes with Node.js)

### Installation Steps:

1. **Clone the repository and install dependencies:**
```bash
git clone <repository-url>
cd TestSynth
npm install
```

2. **Install Playwright browsers:**
```bash
npx playwright install
```

3. **Build the TypeScript project:**
```bash
npm run build
```

### Key Dependencies (automatically installed with npm install):
- `@playwright/test` - Playwright testing framework
- `@playwright/mcp` - Playwright MCP server for browser automation
- `@modelcontextprotocol/sdk` - MCP SDK for protocol communication
- `typescript` - TypeScript compiler
- `yaml` - YAML parsing for accessibility snapshots

## Overview

This project provides automated test generation using Model Context Protocol (MCP) and Playwright. You can generate tests in two ways:

1. **Automatic exploration** of webpages to generate comprehensive tests with real element discovery
2. **Specific test cases** using natural language verify statements

## Quick Start

**Most common command - Generate comprehensive tests from any website:**

```bash
node scripts/generate-tests.js explore <URL> "<Test Name>"
```

**Example:**
```bash
node scripts/generate-tests.js explore https://testautomationpractice.blogspot.com/ "My Test"
```

This will autonomously explore the website and generate comprehensive tests with real element discovery!

---

## Scenario 1: Automatic Website Exploration (Recommended)

### Quick Command

**Generate comprehensive tests by exploring any website:**

```bash
node scripts/generate-tests.js explore <URL> "<Test Name>"
```

### Examples

**Explore a practice automation site:**
```bash
node scripts/generate-tests.js explore https://testautomationpractice.blogspot.com/ "Practice Site Test"
```

**Explore Playwright documentation:**
```bash
node scripts/generate-tests.js explore https://playwright.dev "Playwright Homepage Test"
```

**Explore a todo app:**
```bash
node scripts/generate-tests.js explore https://demo.playwright.dev/todomvc "Todo App Test"
```

### What This Does:

1. **Connects to Playwright MCP server** (`@playwright/mcp`) to browse the website
2. **Captures accessibility snapshot** of the page structure
3. **Autonomously discovers elements**: buttons, forms, links, checkboxes, radio buttons, dropdowns
4. **Generates comprehensive test suite** with:
   - Specific button tests (real button names like "Submit", "Login")
   - Form input tests (actual field placeholders and test data)
   - Checkbox and radio button interactions
   - Dropdown selections
   - Navigation link verification
   - Screenshot capture and error handling

### Example Generated Test Output:
The system will discover and generate tests for elements like:
- **18 buttons** with specific names ("Submit", "Upload File", "Simple Alert")
- **17 textboxes** with real placeholders ("Enter Name", "Enter Email")
- **30 links** with actual text ("Home", "Blog", "Courses")
- **7 checkboxes** (days of the week, preferences)
- **2 radio buttons** (gender selection, options)
- **1 dropdown** (country selection, categories)

---

## Scenario 2: Generate Specific Test Cases Using Natural Language Verify Statements

### Quick Command

```bash
node scripts/generate-tests.js scenarios
```

### Step-by-Step Process

1. **Edit your scenarios file** (`test-data/scenarios.json`):

```json
{
  "scenarios": [
    {
      "id": "my-custom-test",
      "name": "My Custom Test",
      "description": "Test specific functionality I want to verify",
      "url": "https://example.com",
      "verifyStatements": [
        "Verify header is present and contains 'Welcome'",
        "Verify login button exists",
        "Verify contact form is visible",
        "Verify footer contains copyright text"
      ]
    }
  ]
}
```

2. **Run the generation command**:

```bash
node scripts/generate-tests.js scenarios
```

3. **Run your generated tests**:

```bash
npx playwright test tests/generated/my-custom-test.spec.ts
```

### Natural Language Examples You Can Use:

- `"Verify header is present"`
- `"Verify navigation menu exists"`
- `"Verify main heading contains 'Welcome'"`
- `"Verify submit button is disabled"`
- `"Verify email field validation shows error for invalid email"`
- `"Verify search box is visible"`
- `"Verify footer is present"`

---

## Interactive Mode (Advanced)

For more control, use interactive mode:

```bash
node scripts/generate-tests.js interactive
```

**Available commands in interactive mode:**

- `explore <url> [testName]` - Explore and generate tests
- `scenarios` - Generate from scenarios file
- `list` - Show generated test files
- `clean` - Remove generated test files
- `help` - Show help
- `exit` - Exit interactive mode

---

## Running Your Generated Tests

**Run all generated tests:**

```bash
npx playwright test tests/generated/
```

**Run specific test:**

```bash
npx playwright test tests/generated/your-test-name.spec.ts
```

**Run with browser visible (headed mode):**

```bash
npx playwright test tests/generated/your-test-name.spec.ts --headed
```

**Debug mode:**

```bash
npx playwright test tests/generated/your-test-name.spec.ts --debug
```

---

## Tips for Best Results

### For Verify Statements:

- Be specific about elements: `"Verify login button exists"` vs `"Verify button exists"`
- Include expected text: `"Verify header contains 'Welcome'"`
- Use common web elements: header, navigation, form, button, link, etc.

### For Website Exploration:

- Use URLs with rich content for better test generation
- Public demo sites work well (like demo.playwright.dev)
- The system works best with standard web elements

### File Locations:

- **Generated tests**: `tests/generated/`
- **Screenshots**: `screenshots/`
- **Scenarios config**: `test-data/scenarios.json`

---

## Project Structure

```
├── src/                          # TypeScript source files
│   ├── mcp-client.ts            # MCP client integration
│   ├── test-generator.ts        # Test generation logic
│   ├── test-data-manager.ts     # Test data management
│   ├── verify-parser.ts         # Natural language parser
│   └── prompt-loader.ts         # Prompt template loader
├── tests/
│   └── generated/               # Auto-generated test files
├── test-data/
│   ├── scenarios.json           # Test scenarios configuration
│   └── fixtures.json            # Test data fixtures
├── scripts/
│   └── generate-tests.js        # Main generation script
├── screenshots/                 # Test screenshots
└── mcp-config.json             # MCP server configuration
```

## Enhanced Features

### AI-Powered Prompt System

The system now includes sophisticated AI prompts that guide the MCP server to generate higher-quality tests:

- **Exploration Prompts**: Guide autonomous website exploration and comprehensive test generation
- **Scenario Prompts**: Convert natural language verify statements into robust test cases
- **Template Variables**: Dynamic prompt customization with URL, test names, and scenarios

---

## Troubleshooting

**Build the project first:**

```bash
npm run build
```

**Clean generated tests:**

```bash
Remove-Item tests/generated/*.spec.ts -Force  # Windows
rm tests/generated/*.spec.ts                  # Linux/Mac
```

**Check MCP server connection:**
The system uses `@playwright/mcp` to browse websites and analyze page structure for comprehensive test generation.

**Common Issues:**

1. **"Module not found" errors**: Make sure you ran `npm run build` after installation
2. **MCP connection issues**: Ensure `@playwright/mcp` is installed with `npm install`
3. **Browser not found**: Run `npx playwright install` to install browser binaries

That's it! The system handles all the complex MCP integration, autonomous website exploration, element detection, and Playwright test code generation automatically.
