# PowerShell script to start TestSynth Simple UI

Write-Host " Starting TestSynth MCP Explorer (Simple UI)..." -ForegroundColor Green
Write-Host ""

# Check if backend server is running
Write-Host " Checking backend server..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Backend server is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend server not running. Starting it now..." -ForegroundColor Red
    Write-Host "📡 Starting backend API server..." -ForegroundColor Yellow
    $serverPath = Join-Path $PSScriptRoot "ui\server"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$serverPath'; npm start" -WindowStyle Normal
    Write-Host "⏳ Waiting for backend to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

# Start the simple UI server
Write-Host " Starting Simple UI server..." -ForegroundColor Yellow
$uiPath = Join-Path $PSScriptRoot "ui-simple"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$uiPath'; npm start" -WindowStyle Normal

# Wait a moment then open browser
Start-Sleep -Seconds 3
Write-Host "🌐 Opening browser..." -ForegroundColor Yellow
Start-Process "http://localhost:3000"

Write-Host ""
Write-Host "✅ TestSynth UI is starting up!" -ForegroundColor Green
Write-Host " Backend API: http://localhost:3001" -ForegroundColor Cyan
Write-Host " Simple UI: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "The browser should open automatically..." -ForegroundColor Gray
Write-Host "Press any key to exit this script (the UI will continue running)..." -ForegroundColor Gray

$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
