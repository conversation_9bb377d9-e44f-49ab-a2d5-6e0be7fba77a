# MCP Test Generator

Automated Playwright test generation using Model Context Protocol (MCP) for autonomous website exploration and test creation.

## Quick Start

**Generate comprehensive tests from any website:**

```bash
node scripts/generate-tests.js explore <URL> "<Test Name>"
```

**Example:**
```bash
node scripts/generate-tests.js explore https://testautomationpractice.blogspot.com/ "My Test"
```

## Setup

1. **Install dependencies:**
```bash
npm install
npx playwright install
npm run build
```

2. **Generate tests:**
```bash
node scripts/generate-tests.js explore https://example.com "Example Test"
```

3. **Run tests:**
```bash
npx playwright test tests/generated/
```

## Features

- **Autonomous Website Exploration**: Automatically discovers buttons, forms, links, and interactive elements
- **Real Element Discovery**: Generates tests with actual element names and selectors
- **Comprehensive Test Coverage**: Creates tests for forms, navigation, interactions, and validations
- **Natural Language Scenarios**: Convert verify statements into test cases

## Documentation

See [INSTRUCTIONS.md](./INSTRUCTIONS.md) for complete setup instructions, examples, and advanced usage.

## Project Structure

```
├── src/                     # TypeScript source files
├── scripts/generate-tests.js # Main generation script
├── tests/generated/         # Auto-generated test files
├── test-data/              # Test scenarios and fixtures
└── INSTRUCTIONS.md         # Complete documentation
```

## Example Output

The system autonomously discovers and generates tests for:
- **Buttons** with real names ("Submit", "Login", "Upload")
- **Forms** with actual field placeholders and validation
- **Navigation** links and menus
- **Interactive elements** like checkboxes, dropdowns, radio buttons

Generated tests include error handling, screenshots, and comprehensive assertions based on actual page content.
