#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./dist/src/mcp-client.js');

async function debugMCPGeneration() {
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless']
  });

  try {
    console.log('Connecting to MCP server...');
    await mcpClient.connect();
    
    console.log('Exploring website...');
    const explorationResult = await mcpClient.exploreWebsiteWithPrompt('https://testautomationpractice.blogspot.com/', 'Debug Test');
    
    console.log('Exploration result structure:');
    console.log('Keys:', Object.keys(explorationResult));
    
    if (explorationResult.testGenerationResult) {
      console.log('\nTest generation result:');
      console.log('Type:', typeof explorationResult.testGenerationResult);
      console.log('Is array:', Array.isArray(explorationResult.testGenerationResult));
      
      if (typeof explorationResult.testGenerationResult === 'object') {
        console.log('Keys:', Object.keys(explorationResult.testGenerationResult));
        
        if (explorationResult.testGenerationResult.content) {
          console.log('\nGenerated test content:');
          console.log('Content type:', typeof explorationResult.testGenerationResult.content);
          console.log('Content is array:', Array.isArray(explorationResult.testGenerationResult.content));

          if (Array.isArray(explorationResult.testGenerationResult.content)) {
            console.log('Content array length:', explorationResult.testGenerationResult.content.length);
            if (explorationResult.testGenerationResult.content.length > 0) {
              console.log('First element:', explorationResult.testGenerationResult.content[0]);
            }
          } else if (typeof explorationResult.testGenerationResult.content === 'string') {
            console.log('Content preview:', explorationResult.testGenerationResult.content.substring(0, 500) + '...');
          } else {
            console.log('Content object:', JSON.stringify(explorationResult.testGenerationResult.content, null, 2));
          }
        }
      } else {
        console.log('\nGenerated test content preview:');
        console.log(explorationResult.testGenerationResult.substring(0, 500) + '...');
      }
    } else {
      console.log('\nNo testGenerationResult found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mcpClient.disconnect();
  }
}

debugMCPGeneration();
