#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./dist/src/mcp-client.js');
const { PlaywrightTestGenerator } = require('./dist/src/test-generator.js');

async function debugParsing() {
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless']
  });

  const testGenerator = new PlaywrightTestGenerator(mcpClient, {
    outputDir: './tests/generated',
    templateDir: './test-templates'
  });

  try {
    console.log('Connecting to MCP server...');
    await mcpClient.connect();
    
    console.log('Exploring website...');
    const explorationResult = await mcpClient.exploreWebsiteWithPrompt('https://testautomationpractice.blogspot.com/', 'Debug Test');
    
    console.log('Exploration result structure:');
    console.log('Keys:', Object.keys(explorationResult));
    console.log('explorationResult.explorationResult exists:', !!explorationResult.explorationResult);
    console.log('explorationResult.snapshot exists:', !!explorationResult.snapshot);
    
    if (explorationResult.explorationResult) {
      console.log('explorationResult.explorationResult keys:', Object.keys(explorationResult.explorationResult));
      console.log('explorationResult.explorationResult.snapshot exists:', !!explorationResult.explorationResult.snapshot);

      if (explorationResult.explorationResult.snapshot) {
        const snapshot = explorationResult.explorationResult.snapshot;
        console.log('Inner snapshot type:', typeof snapshot);
        console.log('Inner snapshot is array:', Array.isArray(snapshot));
        if (Array.isArray(snapshot)) {
          console.log('Inner snapshot length:', snapshot.length);
          if (snapshot.length > 0) {
            console.log('First element keys:', Object.keys(snapshot[0]));
            console.log('First element text preview:', snapshot[0].text ? snapshot[0].text.substring(0, 200) + '...' : 'No text');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mcpClient.disconnect();
  }
}

debugParsing();
