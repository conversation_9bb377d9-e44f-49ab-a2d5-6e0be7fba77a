#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./dist/src/mcp-client.js');

async function debugSnapshot() {
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless']
  });

  try {
    console.log('Connecting to MCP server...');
    await mcpClient.connect();
    
    console.log('Navigating to test site...');
    const result = await mcpClient.capturePageInfo('https://testautomationpractice.blogspot.com/');
    
    console.log('Snapshot result:');
    console.log(JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mcpClient.disconnect();
  }
}

debugSnapshot();
