#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./dist/src/mcp-client.js');

async function debugYamlParsing() {
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless']
  });

  try {
    console.log('Connecting to MCP server...');
    await mcpClient.connect();
    
    console.log('Exploring website...');
    const explorationResult = await mcpClient.exploreWebsiteWithPrompt('https://testautomationpractice.blogspot.com/', 'Debug Test');
    
    const snapshot = explorationResult.explorationResult.snapshot;
    const snapshotText = snapshot[0].text;
    
    console.log('Looking for YAML section...');
    const yamlMatch = snapshotText.match(/```yaml\n([\s\S]*?)\n```/);
    if (yamlMatch) {
      console.log('Found YAML section!');
      const yamlContent = yamlMatch[1];
      
      // Test button parsing
      const buttonMatches = yamlContent.match(/- button "([^"]*)" \[ref=([^\]]*)\]/g) || [];
      console.log(`Found ${buttonMatches.length} buttons:`);
      buttonMatches.slice(0, 5).forEach((match, i) => {
        console.log(`  ${i + 1}: ${match}`);
      });
      
      // Test textbox parsing
      const textboxMatches = yamlContent.match(/- textbox[^[]*\[ref=([^\]]*)\]/g) || [];
      console.log(`Found ${textboxMatches.length} textboxes:`);
      textboxMatches.slice(0, 5).forEach((match, i) => {
        console.log(`  ${i + 1}: ${match}`);
      });
      
      // Test link parsing
      const linkMatches = yamlContent.match(/- link "([^"]*)" \[ref=([^\]]*)\]/g) || [];
      console.log(`Found ${linkMatches.length} links:`);
      linkMatches.slice(0, 5).forEach((match, i) => {
        console.log(`  ${i + 1}: ${match}`);
      });
      
    } else {
      console.log('No YAML section found!');
      console.log('Snapshot text preview:');
      console.log(snapshotText.substring(0, 500));
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mcpClient.disconnect();
  }
}

debugYamlParsing();
