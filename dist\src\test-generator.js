"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaywrightTestGenerator = void 0;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class PlaywrightTestGenerator {
    constructor(mcpClient, config) {
        this.mcpClient = mcpClient;
        this.config = config;
    }
    async generateTestFromScenario(scenario) {
        try {
            // Load the test template
            const templatePath = path.join(this.config.templateDir, 'basic-test.template.ts');
            const template = await fs.readFile(templatePath, 'utf-8');
            // Generate test steps code
            const testStepsCode = this.generateTestStepsCode(scenario.steps || []);
            const assertionsCode = this.generateAssertionsCode(scenario.steps || []);
            // Replace template placeholders
            let testCode = template
                .replace('{{testSuiteName}}', scenario.name)
                .replace('{{testName}}', scenario.description)
                .replace('{{baseUrl}}', scenario.url)
                .replace('{{testSteps}}', testStepsCode)
                .replace('{{assertions}}', assertionsCode)
                .replace('{{beforeEachCode}}', '// Setup completed')
                .replace('{{afterEachCode}}', '// Cleanup completed');
            return testCode;
        }
        catch (error) {
            console.error('Failed to generate test from scenario:', error);
            throw error;
        }
    }
    async generateFromScenarioWithVerification(scenario) {
        if (scenario.verifyStatements && scenario.verifyStatements.length > 0) {
            // Use MCP to explore the website and generate comprehensive tests
            const explorationResults = await this.mcpClient.exploreWebsiteForVerification(scenario.url, scenario.verifyStatements);
            const testContent = this.buildTestFromVerificationExploration(scenario, explorationResults);
            const filename = `${scenario.id}.spec.ts`;
            const filepath = path.join(this.config.outputDir, filename);
            await fs.writeFile(filepath, testContent, 'utf-8');
            return filepath;
        }
        // Fallback to regular scenario test generation
        const testContent = await this.generateTestFromScenario(scenario);
        const filename = `${scenario.id}.spec.ts`;
        const filepath = path.join(this.config.outputDir, filename);
        await fs.writeFile(filepath, testContent, 'utf-8');
        return filepath;
    }
    async generateFromWebsiteExploration(url, testName) {
        try {
            console.log(`Generating test from website exploration: ${url}`);
            // Use MCP client to explore the website with a prompt
            const explorationResult = await this.mcpClient.exploreWebsiteWithPrompt(url, testName);
            // Build test from exploration results
            const testContent = this.buildTestFromExploration(url, testName, explorationResult);
            // Save the test
            const filename = `${this.sanitizeFilename(testName)}.spec.ts`;
            return await this.saveGeneratedTest(testContent, filename);
        }
        catch (error) {
            console.error('Failed to generate test from website exploration:', error);
            throw error;
        }
    }
    // Generate code for test steps
    generateTestStepsCode(steps) {
        let code = '';
        steps.forEach((step, index) => {
            switch (step.action) {
                case 'navigate':
                    code += `    // Navigate to page\n`;
                    code += `    await page.goto('${step.target}');\n`;
                    code += `    await page.waitForLoadState('networkidle');\n\n`;
                    break;
                case 'click':
                    code += `    // Click element\n`;
                    code += `    await page.locator('${step.target}').click();\n\n`;
                    break;
                case 'fill':
                    code += `    // Fill form field\n`;
                    code += `    await page.locator('${step.target}').fill('${step.value || ''}');\n\n`;
                    break;
                case 'screenshot':
                    code += `    // Take screenshot\n`;
                    code += `    await page.screenshot({ path: '${step.target}' });\n\n`;
                    break;
                default:
                    code += `    // Custom action: ${step.action}\n`;
                    code += `    console.log('Executing custom action: ${step.action}');\n\n`;
            }
        });
        return code;
    }
    // Generate assertions code
    generateAssertionsCode(steps) {
        let code = '';
        steps.forEach(step => {
            if (step.action === 'verify' && step.expected) {
                code += `    // Verify element\n`;
                code += `    await expect(page.locator('${step.target}')).toHaveText('${step.expected}');\n\n`;
            }
        });
        return code;
    }
    async saveGeneratedTest(testCode, filename) {
        try {
            // Ensure output directory exists
            await fs.mkdir(this.config.outputDir, { recursive: true });
            // Generate full file path
            const filePath = path.join(this.config.outputDir, filename);
            // Write the test file
            await fs.writeFile(filePath, testCode, 'utf-8');
            console.log(`Generated test saved to: ${filePath}`);
            return filePath;
        }
        catch (error) {
            console.error('Failed to save generated test:', error);
            throw error;
        }
    }
    async generateTestsFromScenariosFile(scenariosPath) {
        try {
            // Read scenarios file
            const scenariosContent = await fs.readFile(scenariosPath, 'utf-8');
            const scenariosData = JSON.parse(scenariosContent);
            const generatedFiles = [];
            // Generate tests for each scenario
            for (const scenario of scenariosData.scenarios) {
                const testCode = await this.generateTestFromScenario(scenario);
                const filename = `${scenario.id}.spec.ts`;
                const filePath = await this.saveGeneratedTest(testCode, filename);
                generatedFiles.push(filePath);
            }
            return generatedFiles;
        }
        catch (error) {
            console.error('Failed to generate tests from scenarios file:', error);
            throw error;
        }
    }
    async exploreAndGenerateTest(url, testName) {
        try {
            // Use MCP client to properly explore the website with AI prompts
            console.log(`Starting exploration for ${url}...`);
            const explorationResult = await this.mcpClient.exploreWebsiteWithPrompt(url, testName);
            console.log('Exploration completed, result:', JSON.stringify(explorationResult, null, 2));
            // Generate test based on the actual exploration results
            const testContent = this.buildTestFromExploration(url, testName, explorationResult);
            const filename = `${this.sanitizeFilename(testName)}.spec.ts`;
            return await this.saveGeneratedTest(testContent, filename);
        }
        catch (error) {
            console.error('Failed to explore and generate test:', error);
            throw error;
        }
    }
    buildTestFromVerificationExploration(scenario, explorationResults) {
        const verificationTests = scenario.verifyStatements.map((statement, index) => {
            return `
  test('Verification ${index + 1}: ${statement}', async ({ page }) => {
    await page.goto('${scenario.url}');
    await page.waitForLoadState('networkidle');
    
    // Verification logic based on exploration results
    ${this.generateVerificationCode(statement, explorationResults)}
  });`;
        }).join('\n');
        return `import { test, expect } from '@playwright/test';

test.describe('${scenario.name}', () => {
  test.beforeEach(async () => {
    // Common setup
  });
  
${verificationTests}

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: \`screenshots/\${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png\`,
        fullPage: true
      });
    }
  });
});`;
    }
    // Helper method to generate verification code
    generateVerificationCode(statement, explorationResults) {
        // Default verification code
        return `
    // TODO: Generate specific verification based on: "${statement}"
    await expect(page.locator('body')).toBeVisible();
    console.log('Verifying: ${statement.replace(/'/g, "\\'")}');`;
    }
    buildTestFromExploration(url, testName, explorationResult) {
        // Write debug info to file
        const fs = require('fs');
        const debugInfo = {
            timestamp: new Date().toISOString(),
            explorationResultKeys: explorationResult ? Object.keys(explorationResult) : 'null',
            hasGeneratedTestContent: !!(explorationResult && explorationResult.generatedTestContent),
            generatedTestContentType: explorationResult && explorationResult.generatedTestContent ? typeof explorationResult.generatedTestContent : 'undefined'
        };
        fs.writeFileSync('debug-exploration.json', JSON.stringify(debugInfo, null, 2));
        console.log('Building test from exploration results...');
        console.log('Exploration result keys:', explorationResult ? Object.keys(explorationResult) : 'null');
        // Check if we have generated test content from the MCP client
        if (explorationResult && explorationResult.generatedTestContent) {
            console.log('Found generated test content from MCP client');
            return explorationResult.generatedTestContent;
        }
        // Check if we have a test generation result from the MCP server
        if (explorationResult && explorationResult.testGenerationResult) {
            console.log('Found test generation result from MCP server');
            // The MCP server should return the generated test content
            if (explorationResult.testGenerationResult.content) {
                console.log('Using generated test content from MCP server');
                return explorationResult.testGenerationResult.content;
            }
            // If content is in a different structure, try to extract it
            if (typeof explorationResult.testGenerationResult === 'string') {
                console.log('Test generation result is a string, using directly');
                return explorationResult.testGenerationResult;
            }
            // If it's an array with content
            if (Array.isArray(explorationResult.testGenerationResult) &&
                explorationResult.testGenerationResult.length > 0 &&
                explorationResult.testGenerationResult[0].content) {
                console.log('Extracting test content from array result');
                return explorationResult.testGenerationResult[0].content;
            }
        }
        console.warn('No usable test generation result found, falling back to generic test');
        return this.buildGenericTest(url, testName);
    }
    // Fallback method for generic test
    buildGenericTest(url, testName) {
        return `import { test, expect } from '@playwright/test';

test.describe('${testName} - Exploration Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('${url}');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully with key elements', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/${this.sanitizeFilename(testName)}-initial.png',
      fullPage: true
    });
  });

  // More generic tests...
  
  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: \`screenshots/\${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png\`,
        fullPage: true
      });
    }
  });
});
`;
    }
    // Build test from accessibility snapshot
    buildTestFromSnapshot(url, testName, snapshot) {
        console.log('Analyzing accessibility snapshot for test generation...');
        try {
            // Parse the snapshot to extract meaningful elements
            const elements = this.parseAccessibilitySnapshot(snapshot);
            let testContent = `import { test, expect } from '@playwright/test';

test.describe('${testName}', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('${url}');
    await page.waitForLoadState('networkidle');
  });

`;
            // Add page load test
            testContent += `
  test('Page loads successfully', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/${this.sanitizeFilename(testName)}-initial.png',
      fullPage: true
    });
  });
`;
            // Add specific element tests based on snapshot analysis
            if (elements.buttons.length > 0) {
                testContent += `
  test('Interactive buttons are present and clickable', async ({ page }) => {`;
                elements.buttons.slice(0, 5).forEach((button, index) => {
                    const selector = this.generateSelector(button);
                    const description = button.name || button.text || `Button ${index + 1}`;
                    testContent += `
    // Test ${description}
    const button${index} = page.locator('${selector}');
    await expect(button${index}).toBeVisible();
    await expect(button${index}).toBeEnabled();`;
                });
                testContent += `
  });
`;
            }
            // Add form tests
            if (elements.forms.length > 0) {
                testContent += `
  test('Forms are present and functional', async ({ page }) => {`;
                elements.forms.slice(0, 3).forEach((form, index) => {
                    const selector = this.generateSelector(form);
                    testContent += `
    // Test form ${index + 1}
    const form${index} = page.locator('${selector}');
    await expect(form${index}).toBeVisible();`;
                    // Add input field tests
                    if (form.inputs && form.inputs.length > 0) {
                        form.inputs.slice(0, 3).forEach((input, inputIndex) => {
                            const inputSelector = this.generateSelector(input);
                            const testValue = this.getTestValue(input.type);
                            testContent += `

    // Test input field in form ${index + 1}
    const input${index}_${inputIndex} = page.locator('${inputSelector}');
    await expect(input${index}_${inputIndex}).toBeVisible();
    await input${index}_${inputIndex}.fill('${testValue}');`;
                        });
                    }
                });
                testContent += `
  });
`;
            }
            // Add link tests
            if (elements.links.length > 0) {
                testContent += `
  test('Navigation links are present', async ({ page }) => {`;
                elements.links.slice(0, 5).forEach((link, index) => {
                    const selector = this.generateSelector(link);
                    const description = link.name || link.text || `Link ${index + 1}`;
                    testContent += `
    // Test ${description}
    const link${index} = page.locator('${selector}');
    await expect(link${index}).toBeVisible();`;
                });
                testContent += `
  });
`;
            }
            // Add afterEach hook
            testContent += `
  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: \`screenshots/\${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png\`,
        fullPage: true
      });
    }
  });
});
`;
            return testContent;
        }
        catch (error) {
            console.error('Error analyzing snapshot:', error);
            return this.buildGenericTest(url, testName);
        }
    }
    // Parse accessibility snapshot to extract testable elements
    parseAccessibilitySnapshot(snapshot) {
        const elements = {
            buttons: [],
            forms: [],
            links: [],
            inputs: []
        };
        try {
            // The snapshot from @playwright/mcp comes as an array with text content
            if (Array.isArray(snapshot) && snapshot.length > 0 && snapshot[0].text) {
                const snapshotText = snapshot[0].text;
                return this.parsePlaywrightMCPSnapshot(snapshotText);
            }
            // The snapshot might be a string or object
            let snapshotData = snapshot;
            if (typeof snapshot === 'string') {
                // Try to parse if it's JSON
                try {
                    snapshotData = JSON.parse(snapshot);
                }
                catch (_a) {
                    // If not JSON, it might be HTML or text
                    return this.parseSnapshotText(snapshot);
                }
            }
            // Recursively traverse the accessibility tree
            this.traverseAccessibilityTree(snapshotData, elements);
            console.log(`Found ${elements.buttons.length} buttons, ${elements.forms.length} forms, ${elements.links.length} links, ${elements.inputs.length} inputs`);
        }
        catch (error) {
            console.error('Error parsing accessibility snapshot:', error);
        }
        return elements;
    }
    // Traverse accessibility tree to find elements
    traverseAccessibilityTree(node, elements) {
        var _a, _b;
        if (!node)
            return;
        // Check if this node is a testable element
        if (node.role) {
            switch (node.role) {
                case 'button':
                    elements.buttons.push({
                        role: node.role,
                        name: node.name,
                        text: node.text,
                        tag: node.tag,
                        attributes: node.attributes
                    });
                    break;
                case 'link':
                    elements.links.push({
                        role: node.role,
                        name: node.name,
                        text: node.text,
                        tag: node.tag,
                        attributes: node.attributes
                    });
                    break;
                case 'textbox':
                case 'searchbox':
                    elements.inputs.push({
                        role: node.role,
                        name: node.name,
                        text: node.text,
                        tag: node.tag,
                        type: ((_a = node.attributes) === null || _a === void 0 ? void 0 : _a.type) || 'text',
                        attributes: node.attributes
                    });
                    break;
            }
        }
        // Check by tag name as well
        if (node.tag) {
            switch (node.tag.toLowerCase()) {
                case 'form':
                    elements.forms.push({
                        tag: node.tag,
                        role: node.role,
                        name: node.name,
                        attributes: node.attributes,
                        inputs: [] // Will be populated by child traversal
                    });
                    break;
                case 'button':
                    if (!elements.buttons.find((b) => b.name === node.name && b.text === node.text)) {
                        elements.buttons.push({
                            role: node.role || 'button',
                            name: node.name,
                            text: node.text,
                            tag: node.tag,
                            attributes: node.attributes
                        });
                    }
                    break;
                case 'input':
                    if (!elements.inputs.find((i) => i.name === node.name)) {
                        elements.inputs.push({
                            role: node.role || 'textbox',
                            name: node.name,
                            text: node.text,
                            tag: node.tag,
                            type: ((_b = node.attributes) === null || _b === void 0 ? void 0 : _b.type) || 'text',
                            attributes: node.attributes
                        });
                    }
                    break;
                case 'a':
                    if (!elements.links.find((l) => l.name === node.name && l.text === node.text)) {
                        elements.links.push({
                            role: node.role || 'link',
                            name: node.name,
                            text: node.text,
                            tag: node.tag,
                            attributes: node.attributes
                        });
                    }
                    break;
            }
        }
        // Recursively traverse children
        if (node.children && Array.isArray(node.children)) {
            node.children.forEach((child) => this.traverseAccessibilityTree(child, elements));
        }
    }
    // Parse Playwright MCP snapshot format
    parsePlaywrightMCPSnapshot(snapshotText) {
        const elements = {
            buttons: [],
            forms: [],
            links: [],
            inputs: []
        };
        try {
            // Extract the YAML snapshot section
            const yamlMatch = snapshotText.match(/```yaml\n([\s\S]*?)\n```/);
            if (!yamlMatch) {
                console.warn('No YAML snapshot found in MCP response');
                return this.parseSnapshotText(snapshotText);
            }
            const yamlContent = yamlMatch[1];
            // Parse elements from YAML content using regex patterns
            // Look for buttons
            const buttonMatches = yamlContent.match(/- button "([^"]*)" \[ref=([^\]]*)\]/g) || [];
            buttonMatches.forEach((match) => {
                const nameMatch = match.match(/button "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                if (nameMatch && refMatch) {
                    elements.buttons.push({
                        role: 'button',
                        name: nameMatch[1],
                        text: nameMatch[1],
                        ref: refMatch[1],
                        tag: 'button'
                    });
                }
            });
            // Look for textboxes
            const textboxMatches = yamlContent.match(/- textbox[^[]*\[ref=([^\]]*)\]/g) || [];
            textboxMatches.forEach((match) => {
                const nameMatch = match.match(/textbox "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                const name = nameMatch ? nameMatch[1] : 'textbox';
                if (refMatch) {
                    elements.inputs.push({
                        role: 'textbox',
                        name: name,
                        text: name,
                        ref: refMatch[1],
                        tag: 'input',
                        type: 'text'
                    });
                }
            });
            // Look for links
            const linkMatches = yamlContent.match(/- link "([^"]*)" \[ref=([^\]]*)\]/g) || [];
            linkMatches.forEach((match) => {
                const nameMatch = match.match(/link "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                if (nameMatch && refMatch) {
                    elements.links.push({
                        role: 'link',
                        name: nameMatch[1],
                        text: nameMatch[1],
                        ref: refMatch[1],
                        tag: 'a'
                    });
                }
            });
            // Look for checkboxes
            const checkboxMatches = yamlContent.match(/- checkbox "([^"]*)" \[ref=([^\]]*)\]/g) || [];
            checkboxMatches.forEach((match) => {
                const nameMatch = match.match(/checkbox "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                if (nameMatch && refMatch) {
                    elements.inputs.push({
                        role: 'checkbox',
                        name: nameMatch[1],
                        text: nameMatch[1],
                        ref: refMatch[1],
                        tag: 'input',
                        type: 'checkbox'
                    });
                }
            });
            // Look for radio buttons
            const radioMatches = yamlContent.match(/- radio "([^"]*)" \[ref=([^\]]*)\]/g) || [];
            radioMatches.forEach((match) => {
                const nameMatch = match.match(/radio "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                if (nameMatch && refMatch) {
                    elements.inputs.push({
                        role: 'radio',
                        name: nameMatch[1],
                        text: nameMatch[1],
                        ref: refMatch[1],
                        tag: 'input',
                        type: 'radio'
                    });
                }
            });
            // Look for comboboxes (dropdowns)
            const comboboxMatches = yamlContent.match(/- combobox "([^"]*)" \[ref=([^\]]*)\]/g) || [];
            comboboxMatches.forEach((match) => {
                const nameMatch = match.match(/combobox "([^"]*)"/);
                const refMatch = match.match(/\[ref=([^\]]*)\]/);
                if (nameMatch && refMatch) {
                    elements.inputs.push({
                        role: 'combobox',
                        name: nameMatch[1],
                        text: nameMatch[1],
                        ref: refMatch[1],
                        tag: 'select',
                        type: 'select'
                    });
                }
            });
            console.log(`Parsed MCP snapshot: ${elements.buttons.length} buttons, ${elements.forms.length} forms, ${elements.links.length} links, ${elements.inputs.length} inputs`);
            return elements;
        }
        catch (error) {
            console.error('Error parsing Playwright MCP snapshot:', error);
            return this.parseSnapshotText(snapshotText);
        }
    }
    // Parse snapshot text (fallback for non-JSON snapshots)
    parseSnapshotText(snapshotText) {
        const elements = {
            buttons: [],
            forms: [],
            links: [],
            inputs: []
        };
        // Simple regex-based parsing for common elements
        const buttonMatches = snapshotText.match(/button[^>]*>([^<]*)</gi) || [];
        buttonMatches.forEach((match, index) => {
            const text = match.replace(/<[^>]*>/g, '').trim();
            if (text) {
                elements.buttons.push({
                    role: 'button',
                    text: text,
                    tag: 'button',
                    name: text
                });
            }
        });
        const linkMatches = snapshotText.match(/<a[^>]*>([^<]*)</gi) || [];
        linkMatches.forEach((match, index) => {
            const text = match.replace(/<[^>]*>/g, '').trim();
            if (text) {
                elements.links.push({
                    role: 'link',
                    text: text,
                    tag: 'a',
                    name: text
                });
            }
        });
        const inputMatches = snapshotText.match(/<input[^>]*>/gi) || [];
        inputMatches.forEach((match, index) => {
            const typeMatch = match.match(/type=["']([^"']*)/);
            const nameMatch = match.match(/name=["']([^"']*)/);
            const type = typeMatch ? typeMatch[1] : 'text';
            const name = nameMatch ? nameMatch[1] : `input_${index}`;
            elements.inputs.push({
                role: 'textbox',
                tag: 'input',
                type: type,
                name: name
            });
        });
        return elements;
    }
    // Generate appropriate selector for an element
    generateSelector(element) {
        var _a, _b;
        // For MCP elements with ref, use a more specific approach
        if (element.ref) {
            // Use text content for buttons and links when available
            if ((element.role === 'button' || element.role === 'link') && element.text && element.text.length < 50) {
                return `text="${element.text}"`;
            }
            // Use name attribute for inputs when available
            if (element.role === 'textbox' || element.role === 'checkbox' || element.role === 'radio') {
                if (element.name && element.name !== 'textbox') {
                    return `[placeholder="${element.name}"]`;
                }
            }
            // For combobox/select, use a more generic approach
            if (element.role === 'combobox' && element.name) {
                return `select`;
            }
        }
        // Priority: data-testid > name > text > tag
        if ((_a = element.attributes) === null || _a === void 0 ? void 0 : _a['data-testid']) {
            return `[data-testid="${element.attributes['data-testid']}"]`;
        }
        if ((_b = element.attributes) === null || _b === void 0 ? void 0 : _b.id) {
            return `#${element.attributes.id}`;
        }
        if (element.name && element.tag && element.name !== element.role) {
            return `${element.tag}[name="${element.name}"]`;
        }
        if (element.text && element.text.length < 50) {
            return `text="${element.text}"`;
        }
        if (element.tag) {
            return element.tag;
        }
        return 'body'; // fallback
    }
    // Get appropriate test value for input type
    getTestValue(inputType) {
        switch (inputType === null || inputType === void 0 ? void 0 : inputType.toLowerCase()) {
            case 'email':
                return '<EMAIL>';
            case 'password':
                return 'TestPassword123';
            case 'number':
                return '123';
            case 'tel':
                return '************';
            case 'url':
                return 'https://example.com';
            case 'date':
                return '2024-01-01';
            default:
                return 'Test Value';
        }
    }
    // Helper method to sanitize filenames
    sanitizeFilename(name) {
        return name.toLowerCase().replace(/[^a-z0-9]/gi, '-');
    }
}
exports.PlaywrightTestGenerator = PlaywrightTestGenerator;
//# sourceMappingURL=test-generator.js.map