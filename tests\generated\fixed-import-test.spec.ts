import { test, expect } from '@playwright/test';

test.describe('Fixed Import Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('https://testautomationpractice.blogspot.com/');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/fixed-import-test-initial.png',
      fullPage: true
    });
  });

  test('Interactive buttons are present and clickable', async ({ page }) => {
    // Test Submit
    const button0 = page.locator('text="Submit"');
    await expect(button0).toBeVisible();
    // Test Choose File
    const button1 = page.locator('text="Choose File"');
    await expect(button1).toBeVisible();
    // Test Upload Single File
    const button2 = page.locator('text="Upload Single File"');
    await expect(button2).toBeVisible();
    // Test Choose File
    const button3 = page.locator('text="Choose File"');
    await expect(button3).toBeVisible();
    // Test Upload Multiple Files
    const button4 = page.locator('text="Upload Multiple Files"');
    await expect(button4).toBeVisible();
    // Test Submit
    const button5 = page.locator('text="Submit"');
    await expect(button5).toBeVisible();
    // Test START
    const button6 = page.locator('text="START"');
    await expect(button6).toBeVisible();
    // Test Simple Alert
    const button7 = page.locator('text="Simple Alert"');
    await expect(button7).toBeVisible();
  });

  test('Form inputs can be filled', async ({ page }) => {
    // Test Enter Name
    const input0 = page.locator('[placeholder="Enter Name"]');
    await expect(input0).toBeVisible();
    await input0.fill('Test User');
    // Test Enter EMail
    const input1 = page.locator('[placeholder="Enter EMail"]');
    await expect(input1).toBeVisible();
    await input1.fill('<EMAIL>');
    // Test Enter Phone
    const input2 = page.locator('[placeholder="Enter Phone"]');
    await expect(input2).toBeVisible();
    await input2.fill('************');
    // Test Address:
    const input3 = page.locator('[placeholder="Address:"]');
    await expect(input3).toBeVisible();
    await input3.fill('123 Test Street');
    // Test textbox
    const input4 = page.locator('[placeholder="textbox"]');
    await expect(input4).toBeVisible();
    await input4.fill('Test Value');
    // Test textbox
    const input5 = page.locator('[placeholder="textbox"]');
    await expect(input5).toBeVisible();
    await input5.fill('Test Value');
  });

  test('Checkboxes can be selected', async ({ page }) => {
    // Test Sunday
    const checkbox0 = page.locator('text="Sunday"').locator('..').locator('input[type="checkbox"]');
    await expect(checkbox0).toBeVisible();
    await checkbox0.check();
    await expect(checkbox0).toBeChecked();
    // Test Monday
    const checkbox1 = page.locator('text="Monday"').locator('..').locator('input[type="checkbox"]');
    await expect(checkbox1).toBeVisible();
    await checkbox1.check();
    await expect(checkbox1).toBeChecked();
    // Test Tuesday
    const checkbox2 = page.locator('text="Tuesday"').locator('..').locator('input[type="checkbox"]');
    await expect(checkbox2).toBeVisible();
    await checkbox2.check();
    await expect(checkbox2).toBeChecked();
    // Test Wednesday
    const checkbox3 = page.locator('text="Wednesday"').locator('..').locator('input[type="checkbox"]');
    await expect(checkbox3).toBeVisible();
    await checkbox3.check();
    await expect(checkbox3).toBeChecked();
    // Test Thursday
    const checkbox4 = page.locator('text="Thursday"').locator('..').locator('input[type="checkbox"]');
    await expect(checkbox4).toBeVisible();
    await checkbox4.check();
    await expect(checkbox4).toBeChecked();
  });

  test('Radio buttons can be selected', async ({ page }) => {
    // Test Male
    const radio0 = page.locator('text="Male"').locator('..').locator('input[type="radio"]');
    await expect(radio0).toBeVisible();
    await radio0.check();
    await expect(radio0).toBeChecked();
    // Test Female
    const radio1 = page.locator('text="Female"').locator('..').locator('input[type="radio"]');
    await expect(radio1).toBeVisible();
    await radio1.check();
    await expect(radio1).toBeChecked();
  });

  test('Dropdown selections work', async ({ page }) => {
    // Test Country:
    const select0 = page.locator('select').nth(0);
    await expect(select0).toBeVisible();
    await select0.selectOption({ index: 1 });
  });

  test('Navigation links are present', async ({ page }) => {
    // Test Home
    const link0 = page.locator('text="Home"');
    await expect(link0).toBeVisible();
    // Test Udemy Courses
    const link1 = page.locator('text="Udemy Courses"');
    await expect(link1).toBeVisible();
    // Test Online Trainings
    const link2 = page.locator('text="Online Trainings"');
    await expect(link2).toBeVisible();
    // Test Blog
    const link3 = page.locator('text="Blog"');
    await expect(link3).toBeVisible();
    // Test PlaywrightPractice
    const link4 = page.locator('text="PlaywrightPractice"');
    await expect(link4).toBeVisible();
  });

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: `screenshots/${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
        fullPage: true
      });
    }
  });
});
